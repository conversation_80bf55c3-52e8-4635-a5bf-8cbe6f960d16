package tools

import (
	"encoding/json"
	"fwyytool/api/arkgo"
	"fwyytool/api/userprofile"
	"fwyytool/components"
	"fwyytool/consts"
	"fwyytool/service/tools/diffJob"
	"fwyytool/stru"
	"net/http"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

var DiffController diffController

type diffController struct {
}

func (s diffController) DoDiff(ctx *gin.Context) {
	err := diffJob.DiffJobService.Do(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, "success")
}

func (s diffController) DiffDetail(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "desk/datadiff/diffdetail.html", nil)
}

// GetDiffCount 获取差异统计数据
func (s diffController) GetDiffCount(ctx *gin.Context) {
	var param arkgo.GetArkStudentDataDiffResParam
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	resp, err := arkgo.NewClient().GetDiffCountRes(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, resp)
}

// GetDiffOverview 获取差异总览数据
func (s diffController) GetDiffOverview(ctx *gin.Context) {
	var param arkgo.GetArkStudentDataDiffResParam
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	resp, err := arkgo.NewClient().GetDiffOverview(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, resp)
}

// GetDiffRes 获取差异结果数据
func (s diffController) GetDiffRes(ctx *gin.Context) {
	var param arkgo.GetArkStudentDataDiffResParam
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	resp, err := arkgo.NewClient().GetArkStudentDataDiffRes(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, resp)
}

// GetDiffConfig 获取Diff控制配置
func (s diffController) GetDiffConfig(ctx *gin.Context) {
	configKey := "assistantdesk_ark_student_list_data_diff_control_config"

	resp, err := arkgo.NewClient().GetRDConfig(ctx, configKey)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	var diffConfig = make(map[string]stru.APIDiffConfig)
	if len(resp) == 0 {
		base.RenderJsonFail(ctx, components.SystemErr("config is empty"))
		return
	}

	err = json.Unmarshal([]byte(resp), &diffConfig)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, diffConfig)
}

// UpdateDiffConfig 保存Diff控制配置
func (s diffController) UpdateDiffConfig(ctx *gin.Context) {
	var req struct {
		Config string `json:"config" binding:"required"`
	}

	if err := ctx.ShouldBind(&req); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	user, _ := ctx.Get(consts.LOGIN_USER_INFO)
	userInfo := user.(*userprofile.UserInfo)

	err := arkgo.NewClient().UpdateRdConfig(ctx, arkgo.UpdateRdConfigParams{
		Key:            "assistantdesk_ark_student_list_data_diff_control_config",
		Value:          req.Config,
		UpdateStaffUid: int64(userInfo.UserId),
	})
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, "success")
}

// GetDiffHandlerNames 获取差异接口列表
func (s diffController) GetDiffHandlerNames(ctx *gin.Context) {
	var param arkgo.GetDiffHandlerNamesParam
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	resp, err := arkgo.NewClient().GetDiffHandlerNames(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, resp)
}
