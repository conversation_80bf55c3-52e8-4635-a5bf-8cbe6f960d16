package tools

import (
	"encoding/json"
	"fmt"
	"fwyytool/api/arkgo"
	"fwyytool/api/userprofile"
	"fwyytool/components"
	"fwyytool/consts"
	"fwyytool/service/tools/diffJob"
	"fwyytool/service/tools/diffJob/diffRegister"
	"fwyytool/stru"
	"net/http"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

var DiffController diffController

type diffController struct {
}

func (s diffController) DoDiff(ctx *gin.Context) {
	err := diffJob.DiffJobService.Do(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, "success")
}

func (s diffController) DiffDetail(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "desk/datadiff/diffdetail.html", nil)
}

// GetDiffCount 获取差异统计数据
func (s diffController) GetDiffCount(ctx *gin.Context) {
	var param arkgo.GetArkStudentDataDiffResParam
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	resp, err := arkgo.NewClient().GetDiffCountRes(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, resp)
}

// GetDiffOverview 获取差异总览数据
func (s diffController) GetDiffOverview(ctx *gin.Context) {
	var param arkgo.GetArkStudentDataDiffResParam
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	resp, err := arkgo.NewClient().GetDiffOverview(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, resp)
}

// GetDiffRes 获取差异结果数据
func (s diffController) GetDiffRes(ctx *gin.Context) {
	var param arkgo.GetArkStudentDataDiffResParam
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	resp, err := arkgo.NewClient().GetArkStudentDataDiffRes(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, resp)
}

// GetDiffConfig 获取Diff控制配置
func (s diffController) GetDiffConfig(ctx *gin.Context) {
	configKey := "assistantdesk_ark_student_list_data_diff_control_config"

	resp, err := arkgo.NewClient().GetRDConfig(ctx, configKey)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	var diffConfig = make(map[string]stru.APIDiffConfig)
	if len(resp) == 0 {
		base.RenderJsonFail(ctx, components.SystemErr("config is empty"))
		return
	}

	err = json.Unmarshal([]byte(resp), &diffConfig)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, diffConfig)
}

// UpdateDiffConfig 保存Diff控制配置
func (s diffController) UpdateDiffConfig(ctx *gin.Context) {
	var req struct {
		Config string `json:"config" binding:"required"`
	}

	if err := ctx.ShouldBind(&req); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	user, _ := ctx.Get(consts.LOGIN_USER_INFO)
	userInfo := user.(*userprofile.UserInfo)

	err := arkgo.NewClient().UpdateRdConfig(ctx, arkgo.UpdateRdConfigParams{
		Key:            "assistantdesk_ark_student_list_data_diff_control_config",
		Value:          req.Config,
		UpdateStaffUid: int64(userInfo.UserId),
	})
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, "success")
}

// GetDiffHandlerNames 获取差异接口列表
func (s diffController) GetDiffHandlerNames(ctx *gin.Context) {
	var param arkgo.GetDiffHandlerNamesParam
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	resp, err := arkgo.NewClient().GetDiffHandlerNames(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, resp)
}

// ExportDiffData 批量导出差异数据
func (s diffController) ExportDiffData(ctx *gin.Context) {
	var param arkgo.GetArkStudentDataDiffResParam
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	// 获取选中的ID数组
	ids := ctx.QueryArray("ids")
	if len(ids) == 0 {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	// 设置大的页面大小来获取所有选中的记录
	param.PageSize = len(ids)
	param.Page = 1

	resp, err := arkgo.NewClient().GetArkStudentDataDiffRes(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	// 过滤出选中的记录
	var filteredData []*arkgo.GetArkStudentDataDiffResOutPutDataDiff
	idMap := make(map[string]bool)
	for _, id := range ids {
		idMap[id] = true
	}

	for _, item := range resp.DataDiffList {
		if idMap[fmt.Sprintf("%d", item.ID)] {
			filteredData = append(filteredData, item)
		}
	}

	// 设置响应头为JSON文件下载
	ctx.Header("Content-Type", "application/json")
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=diff_export_%d.json", time.Now().Unix()))

	base.RenderJsonSucc(ctx, map[string]interface{}{
		"total":      len(filteredData),
		"data":       filteredData,
		"exportTime": time.Now().Unix(),
	})
}

// RerunDiff 重跑指定的 diff 记录
func (s diffController) RerunDiff(ctx *gin.Context) {
	var req struct {
		IDs []int64 `json:"ids" binding:"required"`
	}

	if err := ctx.ShouldBind(&req); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	// 获取配置
	config, err := arkgo.NewClient().GetRDConfig(ctx, "assistantdesk_ark_student_list_data_diff_control_config")
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}
	var diffConfig = make(map[string]stru.APIDiffConfig)
	if len(config) == 0 {
		base.RenderJsonFail(ctx, components.SystemErr("config is empty"))
		return
	}
	err = json.Unmarshal([]byte(config), &diffConfig)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	// 获取记录信息
	records, err := arkgo.NewClient().GetDiffRecordsByIds(ctx, arkgo.GetDiffRecordsByIdsParams{IDs: req.IDs})
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	// 处理每个记录
	var successIDs []string
	var failedIDs []string
	var errorMap = make(map[string]string)

	for _, record := range records {
		// 重置状态为 0
		err = arkgo.NewClient().UpdateDiffRecords(ctx, arkgo.UpdateDiffRecordsParams{
			ID: record.ID,
			Updates: map[string]interface{}{
				"status": 0,
			},
		})
		if err != nil {
			failedIDs = append(failedIDs, fmt.Sprintf("%d", record.ID))
			errorMap[fmt.Sprintf("%d", record.ID)] = fmt.Sprintf("reset status failed: %s", err.Error())
			continue
		}

		// 立即执行处理
		err = handleOneTask(ctx, record, diffConfig)
		if err != nil {
			failedIDs = append(failedIDs, fmt.Sprintf("%d", record.ID))
			errorMap[fmt.Sprintf("%d", record.ID)] = fmt.Sprintf("execute failed: %s", err.Error())
			// 将状态设置为失败
			_ = arkgo.NewClient().UpdateDiffRecords(ctx, arkgo.UpdateDiffRecordsParams{
				ID: record.ID,
				Updates: map[string]interface{}{
					"status": consts.ArkStudentListDataDiffStatusFailed,
				},
			})
		} else {
			successIDs = append(successIDs, fmt.Sprintf("%d", record.ID))
		}
	}

	base.RenderJsonSucc(ctx, map[string]interface{}{
		"success": successIDs,
		"failed":  failedIDs,
		"errors":  errorMap,
		"message": fmt.Sprintf("处理完成: 成功 %d 个, 失败 %d 个", len(successIDs), len(failedIDs)),
	})
}

// handleOneTask 处理单个 diff 任务
func handleOneTask(ctx *gin.Context, record stru.ArkStudentListDataDiff, diffConfig map[string]stru.APIDiffConfig) error {
	defer func() {
		if err := recover(); err != nil {
			zlog.Warnf(ctx, "diffJobService handleOneTask error, err:%s", err)
			components.Util.PanicTrace(ctx)
		}
	}()

	// diffType = 1 (原地回放)，没到预计执行时间，不处理
	if record.DiffType == consts.ArkStudentListDataDiffTypeUseOld {
		execDay := 7
		config, ok := diffConfig[record.HandlerName]
		if ok {
			execDay = config.ReExecutionDayAfter
		}
		targetTime := time.Unix(record.CreateTime, 0)
		timeAfterNDays := targetTime.AddDate(0, 0, execDay)
		if time.Now().Before(timeAfterNDays) {
			//zlog.Info(ctx, "AddStudentListDataDiffScript not reach time to exec :%v", record.ID)
			return nil
		}
	}

	zlog.Info(ctx, "diffJobService begin to exec :%v", record.ID)

	diffAPI := diffRegister.GetDataDiffAPI(record.HandlerName)
	if diffAPI == nil {
		err := arkgo.NewClient().UpdateDiffRecords(ctx, arkgo.UpdateDiffRecordsParams{
			ID: record.ID,
			Updates: map[string]interface{}{
				"status": consts.ArkStudentListDataDiffStatusNotHandle,
			},
		})
		if err != nil {
			return err
		}
		return nil
	}

	oldJson, err := diffAPI.GetOldData(ctx, &record, diffConfig)
	if err != nil {
		return err
	}

	newJson, err := diffAPI.GetNewData(ctx, &record, diffConfig)
	if err != nil {
		return err
	}

	diffNum, res, err := diffAPI.GetDiffResult(ctx, oldJson, newJson, record, diffConfig)
	if err != nil {
		zlog.Warnf(ctx, "diffJobService handle one task :%v,err:%+v", record.ID, err)
		return err
	}

	zlog.Info(ctx, "diffJobService handle one task :%v,diffNum:%v", record.ID, diffNum)

	if diffNum == 0 {
		err = arkgo.NewClient().UpdateDiffRecords(ctx, arkgo.UpdateDiffRecordsParams{
			ID: record.ID,
			Updates: map[string]interface{}{
				"status":   consts.ArkStudentListDataDiffStatusFinished,
				"diff_num": diffNum,
			},
		})
		if err != nil {
			return err
		}
		return nil
	}

	err = arkgo.NewClient().UpdateDiffRecords(ctx, arkgo.UpdateDiffRecordsParams{
		ID: record.ID,
		Updates: map[string]interface{}{
			"status":      consts.ArkStudentListDataDiffStatusFinished,
			"diff_num":    diffNum,
			"old_data":    oldJson,
			"new_data":    newJson,
			"diff_result": res,
		},
	})
	if err != nil {
		return err
	}

	time.Sleep(100 * time.Millisecond)

	return nil
}
